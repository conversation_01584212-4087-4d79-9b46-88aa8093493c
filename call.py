"""
Call script for the Increment Smart Contract
This script calls the increment function on the deployed contract.
"""

import asyncio
import json
import os
from increment_contract import IncrementContract


# Configuration - Update these values before running
CONFIG = {
    # Replace with your private key in WIF format (same as deployment)
    'private_key': 'L4oHTdDHHdWuGLY1ESxCV7eLbeHDv5oW3gzFSJCQrN6qKmtL9G2i',
    
    # Contract UTXO to spend - this will be updated from deployment info
    'contract_utxo_info': {
        'txid': 'c3652f007792a4fab954b4139b42eb2aa4db86d29256f30551c4dcdc0e27abde',  # Contract transaction ID
        'hex': '01000000019b7745a849b9c84d96bdacad16a2cb5dcfa99eb14d06aa16c4a1cb61ef90d4d5010000006a4730440220759b3522b5d8047cb52055e48b0afbda504de80f716c5ce78e2fd4d9054a7963022045cfc45d3f2509a32fa60e037d0f0c577acb259b31575fa17dc7b3b9c5f23a6d412102ac90f611004b836381e24881ed6617f8184e15d6a4c9f60960be5fcc05b02facffffffff02000000000000000031006a2e7b22636f6e7472616374223a22696e6372656d656e74222c22636f756e74223a312c2276657273696f6e223a317d6c070000000000001976a914a8281e9ffc461e1b54d89f0080f31b83cc33a17388ac00000000',   # Raw transaction hex
        'output_index': 1  # Output index of the funding output (usually 1)
    },
    
    # Files
    'deployment_file': 'contract_deployment.json',
    'call_history_file': 'contract_calls.json'
}


def load_deployment_info():
    """Load deployment information from the JSON file."""
    try:
        if not os.path.exists(CONFIG['deployment_file']):
            print(f"❌ Deployment file not found: {CONFIG['deployment_file']}")
            print("Please run deploy.py first to deploy the contract.")
            return None
            
        with open(CONFIG['deployment_file'], 'r') as f:
            deployment_info = json.load(f)
            
        print(f"📄 Loaded deployment info from: {CONFIG['deployment_file']}")
        print(f"Contract TXID: {deployment_info['contract_txid']}")
        print(f"Initial Count: {deployment_info['initial_count']}")
        
        return deployment_info
        
    except Exception as e:
        print(f"❌ Error loading deployment info: {e}")
        return None


def save_call_history(call_info: dict):
    """Save call information to history file."""
    try:
        # Load existing history
        history = []
        if os.path.exists(CONFIG['call_history_file']):
            with open(CONFIG['call_history_file'], 'r') as f:
                history = json.load(f)
        
        # Add new call
        history.append(call_info)
        
        # Save updated history
        with open(CONFIG['call_history_file'], 'w') as f:
            json.dump(history, f, indent=2)
            
        print(f"📄 Call history updated: {CONFIG['call_history_file']}")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not save call history: {e}")


def validate_config():
    """Validate the configuration before calling."""
    errors = []
    
    if not CONFIG['private_key']:
        errors.append("Private key is required")
    
    if not CONFIG['contract_utxo_info']['txid']:
        errors.append("Contract UTXO txid is required")
        
    if not CONFIG['contract_utxo_info']['hex']:
        errors.append("Contract UTXO hex is required")
    
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease update the CONFIG section in call.py")
        return False
    
    return True


async def increment_contract():
    """Increment the contract counter."""
    print("🔄 Starting Contract Increment Call")
    print("=" * 40)
    
    # Load deployment information
    deployment_info = load_deployment_info()
    if not deployment_info:
        return None
    
    # Validate configuration
    if not validate_config():
        return None
    
    try:
        # Create contract instance with current count
        # Note: In a real implementation, you'd query the blockchain for the current state
        current_count = deployment_info['initial_count']
        
        print(f"📝 Creating contract instance")
        contract = IncrementContract(
            private_key=CONFIG['private_key'],
            initial_count=current_count
        )
        
        print(f"📍 Contract address: {contract.private_key.address()}")
        print(f"🔢 Current count: {contract.get_current_count()}")
        print()
        
        # Call increment function
        print("🔄 Broadcasting increment transaction...")
        new_txid = await contract.increment(CONFIG['contract_utxo_info'])
        
        if new_txid:
            print()
            print("🎉 Increment Successful!")
            print("=" * 25)
            print(f"New TXID: {new_txid}")
            print(f"New Count: {contract.get_current_count()}")
            print(f"Previous Count: {current_count}")
            print()
            
            # Save call information
            call_info = {
                'txid': new_txid,
                'previous_count': current_count,
                'new_count': contract.get_current_count(),
                'previous_txid': CONFIG['contract_utxo_info']['txid'],
                'timestamp': new_txid  # Using txid as timestamp placeholder
            }
            save_call_history(call_info)
            
            print("📋 Next Steps:")
            print("1. Wait for the transaction to be confirmed")
            print("2. Update the contract UTXO info for the next call")
            print("3. Run this script again to increment further")
            print()
            print("🔗 View transaction on WhatsOnChain:")
            print(f"   https://whatsonchain.com/tx/{new_txid}")
            print()
            print("⚠️  Important: To call increment again, you need to:")
            print(f"   1. Update CONFIG['contract_utxo_info']['txid'] to: {new_txid}")
            print("   2. Get the new transaction hex and update 'hex'")
            print("   3. Verify the output_index (usually 1 for funding output)")
            
            return new_txid
        else:
            print("❌ Increment call failed!")
            return None
            
    except Exception as e:
        print(f"💥 Increment error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check that the contract UTXO is unspent")
        print("2. Verify the transaction hex is correct")
        print("3. Ensure the output index points to the funding output")
        print("4. Make sure you have sufficient funds for fees")
        raise


def print_usage_instructions():
    """Print instructions for using the call script."""
    print("📖 Increment Contract Call Instructions")
    print("=" * 45)
    print()
    print("Before running this script, you need to:")
    print()
    print("1. 🚀 Deploy the contract first:")
    print("   - Run deploy.py to deploy the contract")
    print("   - This creates contract_deployment.json")
    print()
    print("2. 🔑 Set your private key:")
    print("   - Use the same private key as deployment")
    print("   - Update CONFIG['private_key'] in this file")
    print()
    print("3. 💰 Set the contract UTXO info:")
    print("   - Use the funding output from the deployment")
    print("   - Update CONFIG['contract_utxo_info'] in this file")
    print("   - txid: deployment transaction ID")
    print("   - hex: raw transaction hex of deployment")
    print("   - output_index: usually 1 (funding output)")
    print()
    print("4. 🔄 Run the increment call:")
    print("   python call.py")
    print()
    print("Example contract UTXO info:")
    print("'contract_utxo_info': {")
    print("    'txid': 'def456...',  # From deployment")
    print("    'hex': '0100000001...',  # Deployment tx hex")
    print("    'output_index': 1  # Funding output")
    print("}")


def show_call_history():
    """Show the history of contract calls."""
    try:
        if not os.path.exists(CONFIG['call_history_file']):
            print("📄 No call history found.")
            return
            
        with open(CONFIG['call_history_file'], 'r') as f:
            history = json.load(f)
            
        if not history:
            print("📄 Call history is empty.")
            return
            
        print("📋 Contract Call History:")
        print("=" * 30)
        for i, call in enumerate(history, 1):
            print(f"{i}. TXID: {call['txid']}")
            print(f"   Count: {call['previous_count']} → {call['new_count']}")
            print(f"   Previous TXID: {call['previous_txid']}")
            print()
            
    except Exception as e:
        print(f"❌ Error reading call history: {e}")


if __name__ == "__main__":
    import sys
    
    # Check for command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "history":
        show_call_history()
        sys.exit(0)
    
    # Check if configuration is set up
    if not CONFIG['private_key'] or not CONFIG['contract_utxo_info']['txid']:
        print_usage_instructions()
        print()
        print("⚠️  Please configure the script before running!")
        print("💡 Tip: Run 'python call.py history' to see call history")
    else:
        # Run the increment call
        try:
            result = asyncio.run(increment_contract())
            if result:
                print(f"\n✅ Contract incremented successfully: {result}")
            else:
                print("\n❌ Increment call failed")
        except KeyboardInterrupt:
            print("\n🛑 Call cancelled by user")
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")

     # source venv/bin/activate?
     # python3 call.py
     
