# BSV Increment Smart Contract

A simple smart contract implementation for the BSV blockchain that maintains an incrementable counter. The contract stores its state in OP_RETURN outputs and can be called to increment the counter value.

## Features

- ✅ Simple increment functionality
- ✅ On-chain state storage using OP_RETURN
- ✅ Contract deployment and calling scripts
- ✅ Transaction history tracking
- ✅ Built with BSV Python SDK

## Files

- `increment_contract.py` - Main smart contract implementation
- `deploy.py` - <PERSON><PERSON>t to deploy the contract to BSV blockchain
- `call.py` - <PERSON><PERSON>t to call the increment function
- `ref_p2pkh.py` - Reference P2PKH transaction example
- `ref_scrypt.py` - Reference scrypt smart contract (TypeScript)

## Prerequisites

1. **Python Environment**
   ```bash
   # Activate your virtual environment
   source venv/bin/activate
   
   # Install BSV SDK (if not already installed)
   pip install bsv-sdk
   ```

2. **BSV Wallet**
   - Private key in WIF format
   - Some BSV for transaction fees and contract funding
   - Access to unspent transaction outputs (UTXOs)

## Quick Start

### 1. Deploy the Contract

1. **Configure deployment** - Edit `deploy.py`:
   ```python
   CONFIG = {
       'private_key': 'your_private_key_in_wif_format',
       'utxo_info': {
           'txid': 'your_utxo_transaction_id',
           'hex': 'raw_transaction_hex',
           'output_index': 1  # Usually 0 or 1
       },
       'initial_count': 0,
       'funding_satoshis': 2000
   }
   ```

2. **Run deployment**:
   ```bash
   python deploy.py
   ```

3. **Successful deployment** creates:
   - Contract on BSV blockchain
   - `contract_deployment.json` with deployment info

### 2. Increment the Counter

1. **Configure call** - Edit `call.py`:
   ```python
   CONFIG = {
       'private_key': 'same_private_key_as_deployment',
       'contract_utxo_info': {
           'txid': 'deployment_transaction_id',
           'hex': 'deployment_transaction_hex',
           'output_index': 1  # Funding output index
       }
   }
   ```

2. **Run increment call**:
   ```bash
   python call.py
   ```

3. **View call history**:
   ```bash
   python call.py history
   ```

## How It Works

### Contract State

The contract stores its state in JSON format within OP_RETURN outputs:

```json
{
  "contract": "increment",
  "count": 42,
  "version": 1
}
```

### Transaction Structure

**Deployment Transaction:**
- Input: Your UTXO
- Output 0: OP_RETURN with initial state (0 satoshis)
- Output 1: Funding output (e.g., 2000 satoshis)
- Output 2: Change output

**Increment Transaction:**
- Input: Previous contract funding output
- Output 0: OP_RETURN with new state (0 satoshis)
- Output 1: New funding output (same amount)

### State Management

1. **Deploy**: Creates initial state with count = 0
2. **Increment**: Reads current state, increments count, creates new state
3. **Funding**: Each transaction maintains contract funding for future calls

## Configuration Guide

### Getting UTXO Information

You need to provide UTXO information for transactions. Here's how to get it:

1. **Find an unspent output** in your wallet
2. **Get the transaction ID** (txid)
3. **Get the raw transaction hex** from a block explorer
4. **Note the output index** (usually 0 or 1)

Example using WhatsOnChain:
```
1. Go to https://whatsonchain.com/
2. Search for your transaction ID
3. Copy the "Raw Transaction" hex
4. Note which output index you want to spend
```

### Private Key Format

The private key must be in WIF (Wallet Import Format):
- Starts with 'K', 'L', or '5'
- Example: `KxYjZjmhi91KyJk9PSgjt2ccBKzpB29zKnGBbdFXuaKJ7QSxLVu4`

## Error Handling

### Common Issues

1. **"Private key is required"**
   - Set CONFIG['private_key'] in the script

2. **"UTXO txid is required"**
   - Set CONFIG['utxo_info']['txid'] with a valid transaction ID

3. **"Broadcast failed"**
   - Check if UTXO is already spent
   - Verify transaction hex is correct
   - Ensure sufficient funds for fees

4. **"Invalid UTXO"**
   - Confirm the output index is correct
   - Check that the UTXO exists and is unspent

### Troubleshooting Tips

1. **Verify UTXOs** on a block explorer before using
2. **Check balances** to ensure sufficient funds
3. **Wait for confirmations** before making subsequent calls
4. **Update UTXO info** after each successful transaction

## Advanced Usage

### Custom Initial Count

Deploy with a custom starting value:
```python
CONFIG = {
    'initial_count': 100,  # Start at 100 instead of 0
    # ... other config
}
```

### Multiple Increments

To increment multiple times:
1. Run `call.py` for first increment
2. Update `contract_utxo_info` with new transaction details
3. Run `call.py` again for second increment
4. Repeat as needed

### State Verification

The contract state can be verified by:
1. Examining OP_RETURN outputs on block explorers
2. Parsing the JSON data from transaction outputs
3. Using the `parse_state_from_script()` method

## Security Considerations

- **Private Key**: Keep your private key secure and never share it
- **Funding**: Only fund contracts with amounts you can afford to lose
- **Testing**: Test on testnet before using mainnet
- **Validation**: Always verify transaction details before broadcasting

## Support

For issues or questions:
1. Check the error messages and troubleshooting tips
2. Verify your configuration matches the examples
3. Ensure you have sufficient BSV for fees
4. Test with small amounts first

## License

This is example code for educational purposes. Use at your own risk.
